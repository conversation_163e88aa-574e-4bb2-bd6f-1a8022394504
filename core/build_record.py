# 构建入住记录数据
from collections import defaultdict
from datetime import date

from django.conf import settings

from core.enum import CheckInStatusEnum, GenderEnum
from core.parse_time import parse_datetime_to_shanghai_time
from customer_service.core_records.enums.baby import FeedingMethodChoice
from customer_service.core_records.serializers.baby import NewbornDailyRequiredRecordDetailSerializer
from customer_service.core_records.serializers.maternal import MaternityDailyRequiredRecordDetailSerializer


def build_admission_data(admission):

    data = {
        'aid': admission.aid,
        'check_in_status': admission.check_in_status,
        'delivery_hospital': admission.delivery_hospital,
        'check_in_status_display': CheckInStatusEnum(admission.check_in_status).label,
        'expected_check_in_date': admission.expected_check_in_date,
        'actual_check_in_date': admission.actual_check_in_date,
        'expected_check_out_date': admission.expected_check_out_date,
        'actual_check_out_date': admission.actual_check_out_date,
        'room_number': admission.room.room_number if admission.room else None,
        'chief_nurse_name': admission.chief_nurse.name if admission.chief_nurse else None,
        'residence_days': admission.residence_days,
        'current_day': None,
        'day_description': None
    }

    today = date.today()

    if admission.check_in_status == CheckInStatusEnum.CHECKED_IN:
        if admission.actual_check_in_date:
            days_since_checkin = (today - admission.actual_check_in_date).days + 1  # +1 因为入住当天算第1天
            data['current_day'] = days_since_checkin
            data['day_description'] = f"入住第{days_since_checkin}天"

    elif admission.check_in_status == CheckInStatusEnum.CHECKED_OUT:
        if admission.actual_check_out_date:
            days_since_checkout = (today - admission.actual_check_out_date).days + 1  # +1 因为出院当天算第1天
            data['current_day'] = days_since_checkout
            data['day_description'] = f"出院第{days_since_checkout}天"

    return data

# 构建产妇每日必填记录趋势图
def build_maternal_daily_required_record_trend(records_queryset):
    """
    构建产妇每日必填记录的趋势图数据
    将体重和体温数据分别组织成趋势图格式供前端图表使用
    当同一日期存在多条记录时，只使用最新的记录（按创建时间）

    Args:
        records_queryset: MaternityDailyRequiredRecord查询集

    Returns:
        dict: 包含体重和体温趋势数据的字典
    """
    if not records_queryset:
        return {
            'weight_trend': [],
            'temperature_trend': []
        }

    if settings.USE_POSTGRES_DB:
        
        records = (records_queryset
                   .order_by('record_date', '-created_at')
                   .distinct('record_date')
                   .order_by('record_date'))
    else:
        all_records = records_queryset.order_by('-created_at')
        unique_records_dict = {}
        for record in all_records:
            date_key = record.record_date
            if date_key not in unique_records_dict:
                unique_records_dict[date_key] = record
        records = sorted(unique_records_dict.values(), key=lambda x: x.record_date)

    weight_trend = [
        {
            'date': record.record_date.strftime('%Y-%m-%d'),
            'value': float(record.weight),
            'display_date': record.record_date.strftime('%m-%d')
        }
        for record in records
    ]

    temperature_trend = [
        {
            'date': record.record_date.strftime('%Y-%m-%d'),
            'value': float(record.temperature),
            'display_date': record.record_date.strftime('%m-%d')
        }
        for record in records
    ]

    return {
        'weight_trend': weight_trend,
        'temperature_trend': temperature_trend,
        'total_records': len(weight_trend),
        'date_range': {
            'start_date': weight_trend[0]['date'] if weight_trend else None,
            'end_date': weight_trend[-1]['date'] if weight_trend else None
        }
    }


# 构建单个新生儿的趋势图数据
def build_single_baby_trend(records):
    """
    构建单个新生儿的趋势图数据

    Args:
        records: 单个新生儿的记录列表

    Returns:
        dict: 包含体重、体温和黄疸趋势数据的字典
    """
    if not records:
        return {
            'weight_trend': [],
            'temperature_trend': [],
            'jaundice_trend': []
        }

    # 按日期排序
    sorted_records = sorted(records, key=lambda x: x.record_date)

    weight_trend = [
        {
            'date': record.record_date.strftime('%Y-%m-%d'),
            'value': float(record.weight),
            'display_date': record.record_date.strftime('%m-%d')
        }
        for record in sorted_records
    ]

    temperature_trend = [
        {
            'date': record.record_date.strftime('%Y-%m-%d'),
            'value': float(record.temperature),
            'display_date': record.record_date.strftime('%m-%d')
        }
        for record in sorted_records
    ]

    jaundice_trend = [
        {
            'date': record.record_date.strftime('%Y-%m-%d'),
            'value': float(record.jaundice),
            'display_date': record.record_date.strftime('%m-%d')
        }
        for record in sorted_records
    ]

    return {
        'weight_trend': weight_trend,
        'temperature_trend': temperature_trend,
        'jaundice_trend': jaundice_trend,
        'total_records': len(weight_trend),
        'date_range': {
            'start_date': weight_trend[0]['date'] if weight_trend else None,
            'end_date': weight_trend[-1]['date'] if weight_trend else None
        }
    }


# 构建新生儿每日必填记录数据（按孩子分组）
def build_babies_daily_records_data(records_queryset, all_babies_queryset=None):
    """
    构建新生儿每日必填记录数据，按孩子分组
    每个孩子包含：基本信息、最后一条记录、趋势图数据

    Args:
        records_queryset: NewbornDailyRequiredRecord查询集
        all_babies_queryset: 所有婴儿的查询集，确保不遗漏没有记录的婴儿

    Returns:
        list: 每个孩子的数据字典列表
    """
    # 按新生儿分组记录
    babies_records = defaultdict(list)

    # 将记录按婴儿分组
    for record in records_queryset:
        babies_records[record.newborn].append(record)

    if all_babies_queryset:
        all_babies = list(all_babies_queryset)
        for baby in all_babies:
            if baby not in babies_records:
                babies_records[baby] = []
    elif not records_queryset:
        return []

    babies_data = []
    for baby, records in babies_records.items():
        # 计算出生天数
        from datetime import date
        birth_date = baby.birth_time.date()
        current_date = date.today()
        days_since_birth = (current_date - birth_date).days

        # 基础婴儿信息
        baby_info = {
            'nid': baby.nid,
            'name': baby.name,
            'gender': baby.gender,
            'gender_display': GenderEnum(baby.gender).label,
            'birth_time': baby.birth_time.strftime('%Y-%m-%d %H:%M:%S'),
            'birth_weight': float(baby.birth_weight),
            'birth_length': float(baby.birth_length),
            'hand_card_number': baby.hand_card_number,
            'days_since_birth': days_since_birth,
        }

        if records:
            # 有记录的情况 - 先去重每个日期只保留最新记录
            # 按创建时间倒序排序，确保最新记录在前
            all_records = sorted(records, key=lambda x: x.created_at, reverse=True)

            # 使用字典去重，保留每个日期的第一条记录（即最新记录）
            unique_records_dict = {}
            for record in all_records:
                date_key = record.record_date
                if date_key not in unique_records_dict:
                    unique_records_dict[date_key] = record

            # 按日期倒序排序，最新日期在前
            sorted_records = sorted(unique_records_dict.values(), key=lambda x: x.record_date, reverse=True)
            latest_record = sorted_records[0]

            # 计算体重增长值（最后一条与倒数第二条的差值）
            weight_growth = 0.0
            if len(sorted_records) >= 2:
                weight_growth = float(latest_record.weight) - float(sorted_records[1].weight)

            baby_data = {
                'baby_info': baby_info,
                'last_record': NewbornDailyRequiredRecordDetailSerializer(latest_record).data,
                'last_record_date': latest_record.record_date.strftime('%Y-%m-%d %H:%M:%S'),
                'weight_growth': round(weight_growth, 2),  # 体重增长值，保留2位小数
                'trend_data': build_single_baby_trend(list(unique_records_dict.values())),  # 使用去重后的记录
                'total_records': len(unique_records_dict)  # 使用去重后的记录数量
            }
        else:
            # 没有记录的情况
            baby_data = {
                'baby_info': baby_info,
                'last_record': None,
                'last_record_date': None,
                'weight_growth': 0.0,
                'trend_data': build_single_baby_trend([]),  # 空记录列表
                'total_records': 0
            }

        babies_data.append(baby_data)

    # 按孩子姓名排序，保证返回顺序一致
    babies_data.sort(key=lambda x: x['baby_info']['name'])

    return babies_data


# 构建产妇每日必填记录数据（包含最后记录和趋势图）
def build_maternity_daily_records_data(records_queryset):
    """
    构建产妇每日必填记录数据，包含最后一条记录和趋势图数据

    Args:
        records_queryset: MaternityDailyRequiredRecord查询集

    Returns:
        dict: 包含最后记录和趋势图数据的字典，如果没有记录则返回None
    """
    if not records_queryset:
        return None

    # 按日期排序，最新的在前面
    sorted_records = list(records_queryset.order_by('-record_date'))
    latest_record = sorted_records[0]

    maternity_data = {
        'last_record': MaternityDailyRequiredRecordDetailSerializer(latest_record).data,
        'last_record_date': latest_record.record_date.strftime('%Y-%m-%d %H:%M:%S'),
        'trend_data': build_maternal_daily_required_record_trend(records_queryset),
        'total_records': len(sorted_records)
    }

    return maternity_data


# 构建单条新生儿喂养记录
def build_single_feeding_record_data(record):

    record_duration = 0
    record_amount = 0

    if record.feeding_method == FeedingMethodChoice.BREAST_FEEDING:
        left_time = record.breast_feeding_left_time or 0
        right_time = record.breast_feeding_right_time or 0
        record_duration = left_time + right_time

    elif record.feeding_method == FeedingMethodChoice.ARTIFICIAL_FEEDING:
        record_amount = record.artificial_feeding_amount or 0

    elif record.feeding_method == FeedingMethodChoice.MIXED_FEEDING:
        record_duration = record.mixed_feeding_self_feeding_time or 0
        record_amount = record.mixed_feeding_human_feeding_ml or 0

    record_data = {
        "record_id": record.record_id,
        "feeding_time": parse_datetime_to_shanghai_time(record.record_time),
        "feeding_type": record.feeding_method,
        "feeding_type_display": record.get_feeding_method_display(),
        "creator_name": record.caregiver_signature or 'N/A',
    }

    if record.feeding_method == FeedingMethodChoice.BREAST_FEEDING:
        record_data["duration"] = record_duration
    elif record.feeding_method == FeedingMethodChoice.ARTIFICIAL_FEEDING:
        record_data["amount"] = record_amount
    elif record.feeding_method == FeedingMethodChoice.MIXED_FEEDING:
        record_data["duration"] = record_duration
        record_data["amount"] = record_amount

    return record_data


# 构建列表新生儿喂养记录列表
def build_feeding_data(queryset, single=False):

    if not queryset:
        return None

    if single:
        first_record = queryset[0]
        target_date = first_record.record_time.date()
        queryset = [record for record in queryset if record.record_time.date() == target_date]

    date_records = {}

    for record in queryset:
        record_date = record.record_time.date()
        record_data = build_single_feeding_record_data(record)

        if record_date not in date_records:
            date_records[record_date] = {
                "record_date": record_date,
                "count": 0,
                "duration": 0,
                "amount": 0,
                "feeding_list": []
            }

        date_records[record_date]["feeding_list"].append(record_data)
        date_records[record_date]["count"] += 1

        if "duration" in record_data:
            date_records[record_date]["duration"] += record_data["duration"]
        if "amount" in record_data:
            date_records[record_date]["amount"] += record_data["amount"]

    result = list(date_records.values())
    result.sort(key=lambda x: x["record_date"], reverse=True)

    if single and result:
        return result[0]

    return result